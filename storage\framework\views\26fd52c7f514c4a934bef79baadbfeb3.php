<div class="<?php echo e($divClass); ?>">
    <div class="form-group">
        <label for="<?php echo e($name); ?>" class="form-label"><?php echo e($label); ?></label><?php if($required): ?> <?php if (isset($component)) { $__componentOriginalbba606fec37ea04333bc269e3e165587 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbba606fec37ea04333bc269e3e165587 = $attributes; } ?>
<?php $component = App\View\Components\Required::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('required'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Required::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbba606fec37ea04333bc269e3e165587)): ?>
<?php $attributes = $__attributesOriginalbba606fec37ea04333bc269e3e165587; ?>
<?php unset($__attributesOriginalbba606fec37ea04333bc269e3e165587); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbba606fec37ea04333bc269e3e165587)): ?>
<?php $component = $__componentOriginalbba606fec37ea04333bc269e3e165587; ?>
<?php unset($__componentOriginalbba606fec37ea04333bc269e3e165587); ?>
<?php endif; ?> <?php endif; ?>
        <input type="tel" name="<?php echo e($name); ?>" id="<?php echo e($id ?? $name); ?>" class="<?php echo e($class); ?>" placeholder="<?php echo e($placeholder); ?>" pattern="^\+\d{1,3}[\d\s\-\(\)]{9,15}$" title="Please enter phone number with country code (e.g., +1234567890)" <?php if($required): ?> required <?php endif; ?> value="<?php echo e($value); ?>">
        <div class="text-xs text-danger">
            <?php echo e(__('Please use with country code. (ex. +91)')); ?>

        </div>
        <div class="invalid-feedback">
            <?php echo e(__('Please enter a valid phone number with country code (e.g., +1234567890)')); ?>

        </div>
    </div>
</div>
<?php /**PATH C:\Program Files\Ampps\www\finhog\resources\views/components/mobile.blade.php ENDPATH**/ ?>