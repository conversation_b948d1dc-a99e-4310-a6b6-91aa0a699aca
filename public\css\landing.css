/* FinHog Landing Page - Optimized CSS */
/* Critical CSS inlined for performance */

:root {
    --color-black: #000000;
    --color-white: #ffffff;
    --color-gray-50: #fafafa;
    --color-gray-100: #f5f5f5;
    --color-gray-200: #e5e5e5;
    --color-gray-300: #d4d4d4;
    --color-gray-400: #a3a3a3;
    --color-gray-500: #737373;
    --color-gray-600: #525252;
    --color-gray-700: #404040;
    --color-gray-800: #262626;
    --color-gray-900: #171717;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Space Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--color-gray-800);
    background-color: var(--color-white);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-display: swap;
}

/* Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.landing-page {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header - Critical above-the-fold content */
.header {
    padding: 1.5rem 0;
    border-bottom: 1px solid var(--color-gray-200);
    background-color: var(--color-white);
    position: relative;
    z-index: 10;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-nav {
    display: flex;
    align-items: center;
    margin-left: auto;
    margin-right: 2rem;
}

.nav-link {
    color: var(--color-gray-700);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: color 0.2s ease;
}

.nav-link:hover {
    color: var(--color-black);
}

.nav-link-active {
    color: var(--color-black);
    font-weight: 600;
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-black);
    text-decoration: none;
    transition: opacity 0.2s ease;
}

.logo:hover {
    opacity: 0.8;
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* Buttons - Optimized for performance */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    border-radius: 0;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    cursor: pointer;
    will-change: transform, background-color;
}

.btn-primary {
    background-color: var(--color-black);
    color: var(--color-white);
    border-color: var(--color-black);
}

.btn-primary:hover {
    background-color: var(--color-gray-800);
    border-color: var(--color-gray-800);
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: transparent;
    color: var(--color-gray-700);
    border-color: var(--color-gray-300);
}

.btn-secondary:hover {
    background-color: var(--color-gray-50);
    border-color: var(--color-gray-400);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
}

.btn-outline {
    background-color: transparent;
    color: var(--color-black);
    border-color: var(--color-black);
}

.btn-outline:hover {
    background-color: var(--color-black);
    color: var(--color-white);
}

/* Hero section - Critical content */
.main-content {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 4rem 0;
}

.hero {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    contain: layout style paint;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--color-black);
    margin-bottom: 1.5rem;
    line-height: 1.1;
    will-change: transform;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--color-gray-600);
    margin-bottom: 3rem;
    line-height: 1.5;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.highlight {
    color: var(--color-black);
    padding: 0.2rem 0.4rem;
    display: inline;
    line-height: 1.4;
    position: relative;
    background: linear-gradient(90deg, var(--color-black) 0%, var(--color-black) 100%);
    background-size: 0% 1px;
    background-repeat: no-repeat;
    background-position: 0% 100%;
    animation: drawUnderline 4s ease-in-out infinite;
}

@keyframes drawUnderline {
    0% {
        background-size: 0% 1px;
    }
    25% {
        background-size: 100% 1px;
        background-position: 0% 100%;
    }
    50% {
        background-size: 100% 1px;
        background-position: 0% 100%;
    }
    75% {
        background-size: 100% 1px;
        background-position: 100% 100%;
    }
    100% {
        background-size: 0% 1px;
        background-position: 100% 100%;
    }
}

.lato-bold-italic {
    font-family: 'Lato', sans-serif;
    font-weight: 700;
    font-style: italic;
}

/* Pricing section */
.pricing-section {
    margin: 6rem 0;
    text-align: center;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0;
    margin-top: 3rem;
    border: 1px solid var(--color-gray-200);
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
}

.pricing-card {
    background: var(--color-white);
    padding: 2.5rem 2rem;
    border-right: 1px solid var(--color-gray-200);
    position: relative;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 300px;
}

.pricing-card:last-child {
    border-right: none;
}

.pricing-card-featured {
    background: var(--color-gray-50);
    transform: scale(1.05);
    z-index: 2;
    border: 2px solid var(--color-black);
}

.featured-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--color-black);
    color: var(--color-white);
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.pricing-header {
    margin-bottom: 1.5rem;
}

.plan-name {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--color-black);
    margin-bottom: 1rem;
}

.plan-price {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.price-monthly {
    font-size: 2rem;
    font-weight: 700;
    color: var(--color-black);
}

.price-period {
    font-size: 1rem;
    font-weight: 400;
    color: var(--color-gray-600);
}

.price-annual {
    font-size: 0.875rem;
    color: var(--color-gray-600);
}

.discount {
    color: var(--color-black);
    font-weight: 600;
}

.plan-description {
    margin-bottom: 2rem;
    flex-grow: 1;
}

.plan-description p {
    color: var(--color-gray-600);
    line-height: 1.5;
}

.plan-cta {
    margin-top: auto;
}

/* Contact page styles */
.contact-hero {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 4rem;
}

.contact-section {
    margin: 4rem 0;
}

.contact-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    max-width: 1000px;
    margin: 0 auto;
}

.contact-form-container {
    background: var(--color-white);
    padding: 2.5rem;
    border: 1px solid var(--color-gray-200);
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-weight: 600;
    color: var(--color-black);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-input,
.form-textarea {
    padding: 0.75rem;
    border: 1px solid var(--color-gray-300);
    background-color: var(--color-white);
    font-family: inherit;
    font-size: 1rem;
    transition: border-color 0.2s ease;
    border-radius: 0;
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--color-black);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.form-error {
    color: #dc2626;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.contact-info {
    background: var(--color-gray-50);
    padding: 2.5rem 2rem;
    border: 1px solid var(--color-gray-200);
}

.contact-info-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--color-black);
    margin-bottom: 2rem;
}

.contact-item {
    margin-bottom: 2rem;
}

.contact-item:last-child {
    margin-bottom: 0;
}

.contact-item-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--color-black);
    margin-bottom: 0.5rem;
}

.contact-item-text {
    color: var(--color-gray-600);
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 0.5rem;
}

.contact-link {
    color: var(--color-black);
    text-decoration: none;
    font-weight: 500;
    border-bottom: 1px solid var(--color-black);
    transition: opacity 0.2s ease;
}

.contact-link:hover {
    opacity: 0.7;
}

/* Alert styles */
.alert {
    padding: 1rem;
    margin-bottom: 1.5rem;
    border: 1px solid;
    font-weight: 500;
}

.alert-success {
    background-color: #f0f9ff;
    border-color: #0ea5e9;
    color: #0c4a6e;
}

.alert-error {
    background-color: #fef2f2;
    border-color: #ef4444;
    color: #991b1b;
}

/* Footer styles */
.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: center;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-link {
    color: var(--color-gray-600);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.2s ease;
}

.footer-link:hover {
    color: var(--color-black);
}

.hero-cta {
    margin-bottom: 4rem;
}

/* Content sections */
.content-sections {
    margin: 4rem 0;
}

.content-section {
    text-align: center;
    margin-bottom: 3rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.section-title {
    font-size: 2rem;
    font-weight: 600;
    color: var(--color-black);
    margin-bottom: 1rem;
    line-height: 1.2;
}

.section-description {
    font-size: 1.125rem;
    color: var(--color-gray-600);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.section-description:last-child {
    margin-bottom: 0;
}

/* Hero problem/solution sections */
.hero-problem {
    margin: 2rem 0;
    text-align: center;
}

.problem-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--color-black);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.problem-text {
    font-size: 1.1rem;
    color: var(--color-gray-700);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

.hero-solution {
    margin: 2rem 0;
    text-align: center;
}

.solution-text {
    font-size: 1.1rem;
    color: var(--color-gray-700);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

/* Benefits section */
.advantage-section {
    text-align: center;
    margin: 4rem 0 2rem;
}

.benefits {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 0;
    margin: 3rem 0;
    border: 1px solid var(--color-gray-200);
}

.benefit {
    padding: 2.5rem;
    border-right: 1px solid var(--color-gray-200);
    border-bottom: 1px solid var(--color-gray-200);
    background: var(--color-white);
    text-align: left;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    min-height: 350px;
}

.benefit:nth-child(2n) {
    border-right: none;
}

.benefit:nth-child(n+5) {
    border-bottom: none;
}

.benefit-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-black);
    margin-bottom: 1rem;
    line-height: 1.2;
}

.benefit-subtitle {
    font-size: 1.1rem;
    color: var(--color-black);
    margin-bottom: 1rem;
    line-height: 1.4;
}

.benefit-description {
    color: var(--color-gray-600);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.benefit-list {
    list-style: none;
    padding: 0;
    margin: 1.5rem 0;
}

.benefit-list li {
    color: var(--color-gray-700);
    line-height: 1.6;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.benefit-outcome {
    font-size: 1rem;
    color: var(--color-gray-800);
    margin-top: 1.5rem;
    font-weight: 500;
}

.feature-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color-black);
    margin-bottom: 0.5rem;
}

.feature-description {
    font-size: 0.875rem;
    color: var(--color-gray-600);
    line-height: 1.5;
}

/* Footer */
.footer {
    padding: 2rem 0;
    border-top: 1px solid var(--color-gray-200);
    background-color: var(--color-gray-50);
}

.footer-content {
    text-align: center;
    color: var(--color-gray-500);
    font-size: 0.875rem;
}

/* Performance optimizations */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Responsive design - Mobile first approach */
@media (max-width: 992px) {
    .features {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(3, 1fr);
    }

    .feature:nth-child(2n) {
        border-right: none;
    }

    .feature:nth-child(3n) {
        border-right: 1px solid var(--color-gray-200);
    }

    .feature:nth-child(n+5) {
        border-bottom: none;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 1px;
    }

    .pricing-card {
        border-right: none;
        border-bottom: 1px solid var(--color-gray-200);
        min-height: auto;
    }

    .pricing-card:last-child {
        border-bottom: none;
    }

    .pricing-card-featured {
        transform: none;
        border: 1px solid var(--color-black);
        border-left: 1px solid var(--color-black);
        border-right: 1px solid var(--color-black);
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-form-container,
    .contact-info {
        padding: 2rem 1.5rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .header-nav {
        margin-left: 0;
        margin-right: 0;
        order: 2;
    }

    .header-actions {
        order: 3;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.125rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .section-description {
        font-size: 1rem;
    }

    .benefits {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(6, auto);
        gap: 0;
    }

    .benefit {
        padding: 2rem;
        border-right: none;
        min-height: auto;
    }

    .benefit:nth-child(n) {
        border-right: none;
    }

    .benefit:nth-child(6) {
        border-bottom: none;
    }

    .benefit-title {
        font-size: 1.25rem;
    }

    .features {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(6, auto);
    }

    .feature {
        aspect-ratio: auto;
        min-height: 200px;
    }

    .feature:nth-child(n) {
        border-right: none;
    }

    .feature:nth-child(n+6) {
        border-bottom: none;
    }
    
    .btn-lg {
        padding: 0.875rem 1.5rem;
        font-size: 0.875rem;
    }

    .contact-form-container,
    .contact-info {
        padding: 1.5rem 1rem;
    }

    .footer-content {
        flex-direction: column;
        gap: 1rem;
    }

    .footer-links {
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .main-content {
        padding: 2rem 0;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.25rem;
    }

    .section-description {
        font-size: 0.9rem;
    }

    .content-sections {
        margin: 2rem 0;
    }

    .content-section {
        margin-bottom: 2rem;
    }
}

/* Print styles for accessibility */
@media print {
    .header-actions,
    .hero-cta {
        display: none;
    }
    
    body {
        background: white;
        color: black;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --color-gray-600: #000000;
        --color-gray-500: #000000;
    }
}

/* Focus styles for accessibility */
.btn:focus,
.logo:focus {
    outline: 2px solid var(--color-black);
    outline-offset: 2px;
}

/* Loading optimization */
.hero-title,
.hero-subtitle {
    opacity: 1;
    transform: translateY(0);
}

/* Preload critical resources */
.btn-primary {
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* GPU acceleration for smooth animations */
.btn,
.logo {
    transform: translateZ(0);
    backface-visibility: hidden;
}
