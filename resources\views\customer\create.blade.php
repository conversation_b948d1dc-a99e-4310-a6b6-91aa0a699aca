<form action="{{ url('customer') }}" method="POST" class="needs-validation" novalidate>
@csrf
<!-- Debug: Form action is {{ url('customer') }} -->
<div class="modal-body">
    <h6 class="sub-title">{{ __('Basic Info') }}</h6>
    <div class="row">
        <div class="col-lg-4 col-md-4 col-sm-6">
            <div class="form-group">
                <label for="name" class="form-label">{{ __('Name') }}</label><x-required></x-required>
                <div class="form-icon-user">
                    <input type="text" name="name" id="name" class="form-control" required placeholder="{{ __('Enter Name') }}">
                </div>
            </div>
        </div>

        <x-mobile  div-class="col-md-4 " name="contact" label="{{ __('Contact') }}" placeholder="{{ __('Enter Contact (e.g., +1234567890)') }}" required></x-mobile>

        <div class="col-lg-4 col-md-4 col-sm-6">
            <div class="form-group">
                <label for="email" class="form-label">{{ __('Email') }}</label><x-required></x-required>
                <div class="form-icon-user">
                    <input type="email" name="email" id="email" class="form-control" required placeholder="{{ __('Enter Email') }}">
                </div>
            </div>
        </div>
        <input type="hidden" name="role" value="company">
        <div class="col-lg-4 col-md-4 col-sm-6">
            <div class="form-group">
                <label for="tax_number" class="form-label">{{ __('Tax Number') }}</label>
                <div class="form-icon-user">
                    <input type="text" name="tax_number" id="tax_number" class="form-control" placeholder="{{ __('Enter Tax Number') }}">
                </div>
            </div>
        </div>
        @if (!$customFields->isEmpty())
            <div class="col-lg-4 col-md-4 col-sm-6">
                <div class="tab-pane fade show" id="tab-2" role="tabpanel">
                    @include('customFields.formBuilder')
                </div>
            </div>
        @endif
    </div>

    <h6 class="sub-title">{{ __('Billing Address') }}</h6>
    <div class="row">
        <div class="col-lg-6 col-md-6 col-sm-6">
            <div class="form-group">
                <label for="billing_name" class="form-label">{{ __('Name') }}</label>
                <div class="form-icon-user">
                    <input type="text" name="billing_name" id="billing_name" class="form-control" placeholder="{{ __('Enter Name') }}">
                </div>
            </div>
        </div>

        <x-mobile  div-class="col-md-6 " name="billing_phone" label="{{ __('Phone') }}" placeholder="{{ __('Enter Phone (e.g., +1234567890)') }}" ></x-mobile>

        <div class="col-md-12">
            <div class="form-group">
                <label for="billing_address" class="form-label">{{ __('Address') }}</label>
                <div class="input-group">
                    <textarea name="billing_address" id="billing_address" class="form-control" rows="3" placeholder="{{ __('Enter Address') }}"></textarea>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-6">
            <div class="form-group">
                <label for="billing_city" class="form-label">{{ __('City') }}</label>
                <div class="form-icon-user">
                    <input type="text" name="billing_city" id="billing_city" class="form-control" placeholder="{{ __('Enter City') }}">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-6">
            <div class="form-group">
                <label for="billing_state" class="form-label">{{ __('State') }}</label>
                <div class="form-icon-user">
                    <input type="text" name="billing_state" id="billing_state" class="form-control" placeholder="{{ __('Enter State') }}">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-6">
            <div class="form-group">
                <label for="billing_country" class="form-label">{{ __('Country') }}</label>
                <div class="form-icon-user">
                    <input type="text" name="billing_country" id="billing_country" class="form-control" placeholder="{{ __('Enter Country') }}">
                </div>
            </div>
        </div>

        <div class="col-lg-6 col-md-6 col-sm-6">
            <div class="form-group">
                <label for="billing_zip" class="form-label">{{ __('Zip Code') }}</label>
                <div class="form-icon-user">
                    <input type="text" name="billing_zip" id="billing_zip" class="form-control" placeholder="{{ __('Enter Zip Code') }}">
                </div>
            </div>
        </div>

    </div>

    <div class="col-md-12 text-end">
        <input type="button" id="billing_data" value="Shipping Same As Billing" class="btn btn-primary">
    </div>
    <h6 class="sub-title">{{ __('Shipping Address') }}</h6>
    <div class="row">
        <div class="col-lg-6 col-md-6 col-sm-6">
            <div class="form-group">
                <label for="shipping_name" class="form-label">{{ __('Name') }}</label>
                <div class="form-icon-user">
                    <input type="text" name="shipping_name" id="shipping_name" class="form-control" placeholder="{{ __('Enter Name') }}">
                </div>
            </div>
        </div>

        <x-mobile  div-class="col-md-6 " name="shipping_phone" label="{{ __('Phone') }}" placeholder="{{ __('Enter Phone (e.g., +1234567890)') }}"></x-mobile>

        <div class="col-md-12">
            <div class="form-group">
                <label for="shipping_address" class="form-label">{{ __('Address') }}</label>
                <div class="input-group">
                    <textarea name="shipping_address" id="shipping_address" class="form-control" rows="3" placeholder="{{ __('Enter Address') }}"></textarea>
                </div>
            </div>
        </div>


        <div class="col-lg-6 col-md-6 col-sm-6">
            <div class="form-group">
                <label for="shipping_city" class="form-label">{{ __('City') }}</label>
                <div class="form-icon-user">
                    <input type="text" name="shipping_city" id="shipping_city" class="form-control" placeholder="{{ __('Enter City') }}">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-6">
            <div class="form-group">
                <label for="shipping_state" class="form-label">{{ __('State') }}</label>
                <div class="form-icon-user">
                    <input type="text" name="shipping_state" id="shipping_state" class="form-control" placeholder="{{ __('Enter State') }}">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-6">
            <div class="form-group">
                <label for="shipping_country" class="form-label">{{ __('Country') }}</label>
                <div class="form-icon-user">
                    <input type="text" name="shipping_country" id="shipping_country" class="form-control" placeholder="{{ __('Enter Country') }}">
                </div>
            </div>
        </div>


        <div class="col-lg-6 col-md-6 col-sm-6">
            <div class="form-group">
                <label for="shipping_zip" class="form-label">{{ __('Zip Code') }}</label>
                <div class="form-icon-user">
                    <input type="text" name="shipping_zip" id="shipping_zip" class="form-control" placeholder="{{ __('Enter Zip Code') }}">
                </div>
            </div>
        </div>

    </div>
</div>
<div class="modal-footer">
    <input type="button" value="{{ __('Cancel') }}" class="btn btn-light" data-bs-dismiss="modal">
    <input type="submit" value="{{ __('Create') }}" class="btn btn-primary" id="create-customer-btn">
</div>
</form>

<script>
$(document).ready(function() {
    console.log('Customer create form script loaded');

    // Test function to bypass validation
    window.testFormSubmission = function() {
        console.log('Test form submission triggered');
        var form = document.querySelector('form.needs-validation');
        if (form) {
            console.log('Form found, attempting to submit...');
            console.log('Form action:', form.action);
            console.log('Form method:', form.method);
            // Temporarily remove validation
            form.classList.remove('needs-validation');
            form.noValidate = false;
            form.submit();
        } else {
            console.log('Form not found!');
        }
    };

    // Password switch handler removed - login toggle is now hidden

    // Login enable handler removed - login toggle is now hidden

    // Add phone number validation feedback
    $(document).on('input', 'input[pattern*="\\\\+\\\\d"]', function() {
        var phoneInput = $(this);
        var phoneValue = phoneInput.val().trim();
        var pattern = /^\+\d{1,3}\d{9,13}$/;

        if (phoneValue && !pattern.test(phoneValue)) {
            phoneInput[0].setCustomValidity('Please enter phone number with country code (e.g., +1234567890)');
        } else {
            phoneInput[0].setCustomValidity('');
        }
    });

    // Auto-format phone number input
    $(document).on('input', 'input[name="contact"], input[name="billing_phone"], input[name="shipping_phone"]', function() {
        var input = $(this);
        var value = input.val();

        // Remove any characters that aren't digits, +, spaces, dashes, or parentheses
        value = value.replace(/[^\d+\s\-\(\)]/g, '');

        // If the value doesn't start with + and has digits, add it
        if (value && /\d/.test(value) && !value.startsWith('+')) {
            value = '+' + value;
        }

        input.val(value);

        // Clear any custom validity message when user types
        input[0].setCustomValidity('');
    });

    // Debug: Check if Create button click is detected
    $(document).on('click', 'input[type="submit"][value*="Create"]', function(e) {
        console.log('Create button clicked!');
        console.log('Button:', this);
        console.log('Form:', $(this).closest('form')[0]);
    });

    // Debug: Check form submission
    $(document).on('submit', 'form.needs-validation', function(e) {
        console.log('Form submit event triggered');
        console.log('Form:', this);
        console.log('Form action:', this.action);
        console.log('Form method:', this.method);

        // Check if form is valid
        var isValid = this.checkValidity();
        console.log('Form is valid:', isValid);

        if (!isValid) {
            console.log('Form validation failed, preventing submission');
            e.preventDefault();
            e.stopPropagation();

            // Find ALL invalid fields and log them
            var invalidFields = this.querySelectorAll(':invalid');
            console.log('Number of invalid fields:', invalidFields.length);

            invalidFields.forEach(function(field, index) {
                console.log('Invalid field ' + (index + 1) + ':', {
                    name: field.name,
                    value: field.value,
                    validationMessage: field.validationMessage,
                    required: field.required,
                    pattern: field.pattern,
                    type: field.type
                });
            });

            // Focus on first invalid field
            if (invalidFields.length > 0) {
                invalidFields[0].focus();

                // Show user-friendly error message
                var firstField = invalidFields[0];
                var message = 'Please fix the following error: ' + firstField.validationMessage;
                if (firstField.name === 'contact' || firstField.name === 'billing_phone' || firstField.name === 'shipping_phone') {
                    message = 'Please enter a valid phone number with country code (e.g., +1234567890)';
                }

                // Show toast notification
                if (typeof show_toastr === 'function') {
                    show_toastr('error', message, 'error');
                } else {
                    alert(message);
                }
            }
        } else {
            console.log('Form validation passed, allowing submission');
        }

        this.classList.add('was-validated');
    });
});
</script>
